import Plan from '../models/Plan'
import PlanItem from '../models/PlanItem'
import PlanHistory from '../models/PlanHistory'
import MeditationContent from '../models/MeditationContent'
import { Op } from 'sequelize'
import moment from 'moment'

export default class PlanController {
  /**
   * @swagger
   * /plan/daily:
   *   get:
   *     tags:
   *       - 计划模块
   *     summary: 获取每日计划
   *     description: 获取指定日期前后三天的计划列表
   *     security:
   *       - Bearer: []
   *     parameters:
   *       - in: query
   *         name: date
   *         schema:
   *           type: string
   *           format: date
   *           default: 当前日期
   *         description: 目标日期 (YYYY-MM-DD)
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 data:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: integer
   *                         description: 计划ID
   *                       user_id:
   *                         type: integer
   *                         description: 用户ID
   *                       plan_date:
   *                         type: string
   *                         format: date
   *                         description: 计划日期
   *                       items:
   *                         type: array
   *                         description: 计划项列表
   *       500:
   *         description: 服务器内部错误
   *         content:
   *           application/json:
   *             schema:
   *               $ref: '#/components/schemas/ApiResponse'
   */
  static async getDailyPlans(ctx) {
    const userId = ctx.state.user.id
    const { date = moment().format('YYYY-MM-DD') } = ctx.query

    try {
      const targetDate = moment(date)
      const startDate = targetDate.clone().subtract(3, 'days').format('YYYY-MM-DD')
      const endDate = targetDate.clone().add(3, 'days').format('YYYY-MM-DD')

      const plans = await Plan.findAll({
        where: {
          user_id: userId,
          plan_date: {
            [Op.between]: [startDate, endDate]
          }
        },
        include: [{
          model: PlanItem,
          as: 'items',
          include: [{
            model: MeditationContent,
            as: 'meditation'
          }],
          order: [['seq', 'ASC']]
        }],
        order: [['plan_date', 'ASC']]
      })

      ctx.body = {
        code: 200,
        data: plans
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取计划失败',
        error: error.message
      }
    }
  }

  // 添加冥想内容到计划
  static async addToPlan(ctx) {
    const userId = ctx.state.user.id
    const { meditation_id, plan_date = moment().format('YYYY-MM-DD') } = ctx.request.body

    if (!meditation_id) {
      ctx.body = {
        code: 400,
        message: '冥想内容ID不能为空'
      }
      return
    }

    try {
      // 查找或创建计划
      let plan = await Plan.findOne({
        where: { user_id: userId, plan_date }
      })

      if (!plan) {
        plan = await Plan.create({
          user_id: userId,
          plan_date
        })
      }

      // 检查是否已存在该项目
      const existingItem = await PlanItem.findOne({
        where: { plan_id: plan.id, meditation_id }
      })

      if (existingItem) {
        ctx.body = {
          code: 400,
          message: '该冥想内容已在计划中'
        }
        return
      }

      // 获取当前最大序号
      const maxSeq = await PlanItem.max('seq', {
        where: { plan_id: plan.id }
      }) || 0

      // 添加计划项
      const planItem = await PlanItem.create({
        plan_id: plan.id,
        meditation_id,
        seq: maxSeq + 1
      })

      ctx.body = {
        code: 200,
        message: '添加到计划成功',
        data: planItem
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '添加到计划失败',
        error: error.message
      }
    }
  }

  // 从计划中删除冥想内容
  static async removeFromPlan(ctx) {
    const userId = ctx.state.user.id
    const { id } = ctx.params

    try {
      const planItem = await PlanItem.findOne({
        where: { id },
        include: [{
          model: Plan,
          as: 'plan',
          where: { user_id: userId }
        }]
      })

      if (!planItem) {
        ctx.body = {
          code: 404,
          message: '计划项不存在'
        }
        return
      }

      await planItem.destroy()

      ctx.body = {
        code: 200,
        message: '从计划中删除成功'
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '删除失败',
        error: error.message
      }
    }
  }

  // 完成计划项
  static async completePlanItem(ctx) {
    const userId = ctx.state.user.id
    const { id } = ctx.params

    try {
      const planItem = await PlanItem.findOne({
        where: { id },
        include: [{
          model: Plan,
          as: 'plan',
          where: { user_id: userId }
        }]
      })

      if (!planItem) {
        ctx.body = {
          code: 404,
          message: '计划项不存在'
        }
        return
      }

      // 更新完成状态
      await planItem.update({
        completed: true,
        completed_at: new Date()
      })

      // 添加到历史记录
      await PlanHistory.create({
        user_id: userId,
        meditation_id: planItem.meditation_id
      })

      ctx.body = {
        code: 200,
        message: '计划项完成',
        data: planItem
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '完成计划项失败',
        error: error.message
      }
    }
  }

  // 获取历史计划和完成情况
  static async getPlanHistory(ctx) {
    const userId = ctx.state.user.id
    const { page = 1, limit = 10, start_date, end_date } = ctx.query

    try {
      const offset = (page - 1) * limit
      const whereCondition = { user_id: userId }

      if (start_date && end_date) {
        whereCondition.completed_at = {
          [Op.between]: [start_date, end_date]
        }
      }

      const history = await PlanHistory.findAndCountAll({
        where: whereCondition,
        include: [{
          model: MeditationContent,
          as: 'meditation'
        }],
        limit: parseInt(limit),
        offset: offset,
        order: [['completed_at', 'DESC']]
      })

      ctx.body = {
        code: 200,
        data: {
          total: history.count,
          page: parseInt(page),
          limit: parseInt(limit),
          items: history.rows
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取历史计划失败',
        error: error.message
      }
    }
  }

  // 获取计划统计
  static async getPlanStats(ctx) {
    const userId = ctx.state.user.id
    const { start_date, end_date } = ctx.query

    try {
      const whereCondition = { user_id: userId }
      
      if (start_date && end_date) {
        whereCondition.plan_date = {
          [Op.between]: [start_date, end_date]
        }
      }

      // 获取计划总数
      const totalPlans = await Plan.count({
        where: whereCondition
      })

      // 获取完成的计划项数量
      const completedItems = await PlanItem.count({
        where: { completed: true },
        include: [{
          model: Plan,
          as: 'plan',
          where: whereCondition
        }]
      })

      // 获取总计划项数量
      const totalItems = await PlanItem.count({
        include: [{
          model: Plan,
          as: 'plan',
          where: whereCondition
        }]
      })

      ctx.body = {
        code: 200,
        data: {
          total_plans: totalPlans,
          total_items: totalItems,
          completed_items: completedItems,
          completion_rate: totalItems > 0 ? (completedItems / totalItems * 100).toFixed(2) : 0
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取计划统计失败',
        error: error.message
      }
    }
  }
}